import React, { useRef, useState } from 'react';
import {
  View,
  FlatList,
  StyleSheet,
  StatusBar,
  Dimensions,
  TouchableOpacity,
  Text,
} from 'react-native';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import { StackParamList } from '../../../navigations/StackNavigator';
import { colors, fonts, sizes } from '../../../theme/theme';
import { AppButton } from '../../../componets';

type Props = NativeStackScreenProps<StackParamList, 'ScannerOnboarding'>;

const { width: screenWidth } = Dimensions.get('window');

const SCANNER_ONBOARDING = [
  {
    id: 1,
    title: "Scanner l'écran",
    description:
      "La fonction de scan permet de scanner les valeurs de l'appareil à l'aide de l'appareil photo du smartphone.",
    subDescription:
      "Les valeurs sont automatiquement reconnues et enregistrées dans l'application. <PERSON><PERSON>, il n'est pas nécessaire de saisir manuellement les valeurs dans l'application.",
    icon: 'phone-scan',
    buttonText: 'Suivant',
  },
  {
    id: 2,
    title: "Positionnement correct de l'écran",
    description:
      "Positionnez l'écran de l'appareil à l'intérieur de la zone marquée.",
    subDescription:
      "Remarque : Veillez à ce que les conditions de luminosité soient bonnes et qu'aucun reflet lumineux ne soit visible sur l'écran.",
    icon: 'phone-position',
    buttonText: 'Suivant',
  },
  {
    id: 3,
    title: 'Vérifier les valeurs',
    description:
      "Une fois les valeurs reconnues avec succès, elles s'affichent.",
    subDescription:
      "Si les valeurs ne sont pas correctes, vous pouvez les modifier avant d'enregistrer.",
    icon: 'phone-verify',
    buttonText: 'Commencer le scan',
  },
];

const ScannerOnboarding: React.FC<Props> = ({ navigation }) => {
  const flatListRef = useRef<FlatList>(null);
  const [currentIndex, setCurrentIndex] = useState(0);

  const handleNext = () => {
    if (currentIndex < SCANNER_ONBOARDING.length - 1) {
      const nextIndex = currentIndex + 1;
      flatListRef.current?.scrollToIndex({ index: nextIndex });
      setCurrentIndex(nextIndex);
    } else {
      navigation.goBack(); // Change to scanner screen if needed
    }
  };

  const handleScroll = (event: any) => {
    const slideSize = event.nativeEvent.layoutMeasurement.width;
    const index = Math.round(event.nativeEvent.contentOffset.x / slideSize);
    setCurrentIndex(index);
  };

  const renderIcon = (icon: string) => {
    return (
      <View style={styles.iconContainer}>
        <View style={styles.iconPlaceholder}>
          <Text>{icon}</Text>
        </View>
      </View>
    );
  };

  const renderItem = ({ item }: { item: (typeof SCANNER_ONBOARDING)[0] }) => {
    return (
      <View style={styles.slide}>
        {renderIcon(item.icon)}
        <Text style={styles.title}>{item.title}</Text>
        <Text style={styles.description}>{item.description}</Text>
        <Text style={styles.subDescription}>{item.subDescription}</Text>
      </View>
    );
  };

  const renderPaginationDots = () => {
    return (
      <View style={styles.paginationDots}>
        {SCANNER_ONBOARDING.map((_, index) => (
          <View
            key={index}
            style={[
              styles.dot,
              currentIndex === index ? styles.activeDot : styles.inactiveDot,
            ]}
          />
        ))}
      </View>
    );
  };

  return (
    <View style={styles.container}>
      {/* Header
      <View style={styles.header}>
        <TouchableOpacity onPress={handleBack} style={styles.backButton}>
          <Text style={styles.backText}>←</Text>
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Scan avec la caméra</Text>
        <View style={styles.placeholder} />
      </View> */}

      {/* Slides */}
      <FlatList
        ref={flatListRef}
        data={SCANNER_ONBOARDING}
        horizontal
        pagingEnabled
        scrollEnabled={true}
        showsHorizontalScrollIndicator={false}
        keyExtractor={item => item.id.toString()}
        renderItem={renderItem}
        onMomentumScrollEnd={handleScroll}
      />

      {/* Footer */}
      <View style={styles.bottomContainer}>
        {renderPaginationDots()}
        <AppButton
          title={SCANNER_ONBOARDING[currentIndex].buttonText}
          onPress={handleNext}
          containerStyle={styles.nextButton}
        />
      </View>
    </View>
  );
};

export default ScannerOnboarding;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background_color,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: sizes.paddingHorizontal,
    paddingTop: 50,
    paddingBottom: 20,
    borderBottomWidth: 1,
    borderBottomColor: colors.grey_20,
  },
  backButton: {
    width: 40,
    alignItems: 'center',
  },
  backText: {
    fontSize: 24,
    color: colors.grey_90,
  },
  headerTitle: {
    fontSize: 18,
    fontFamily: fonts.Catamaran_SemiBold,
    color: colors.grey_90,
  },
  placeholder: {
    width: 40,
  },
  slide: {
    width: screenWidth,
    paddingHorizontal: sizes.paddingHorizontal,
    alignItems: 'center',
    justifyContent: 'center',
  },
  iconContainer: {
    marginBottom: 40,
  },
  iconPlaceholder: {
    width: 200,
    height: 200,
    borderRadius: 100,
    backgroundColor: colors.grey_10,
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 2,
    borderColor: colors.grey_20,
  },
  title: {
    fontSize: 24,
    fontFamily: fonts.Catamaran_SemiBold,
    color: colors.grey_90,
    textAlign: 'center',
    marginBottom: 20,
  },
  description: {
    fontSize: 16,
    fontFamily: fonts.NotoSans_Medium,
    color: colors.grey_80,
    textAlign: 'center',
    marginBottom: 16,
    lineHeight: 24,
  },
  subDescription: {
    fontSize: 14,
    fontFamily: fonts.NotoSans_Regular,
    color: colors.grey_60,
    textAlign: 'center',
    fontStyle: 'italic',
    lineHeight: 20,
  },
  bottomContainer: {
    paddingHorizontal: sizes.paddingHorizontal,
    paddingBottom: 30,
    alignItems: 'center',
  },
  paginationDots: {
    flexDirection: 'row',
    marginBottom: 30,
  },
  dot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginHorizontal: 4,
  },
  activeDot: {
    width: 24,
    backgroundColor: colors.primary,
  },
  inactiveDot: {
    backgroundColor: colors.grey_30,
  },
  nextButton: {
    width: '100%',
    marginBottom: 10,
  },
});
